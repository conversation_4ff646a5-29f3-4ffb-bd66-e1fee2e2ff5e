import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'node', // Use node environment for Playwright tests
    globals: true,
    // Each worker creates its own Electron instance, no shared setup needed
    globalTeardown: './e2e/global-teardown.ts',
    include: [
      'e2e/**/*.e2e.ts', // All e2e tests now use Playwright/Electron setup
    ],
    exclude: [
      'tests/**/*',                 // Exclude unit tests
    ],
    testTimeout: 120000, // 2 minutes for e2e tests
    hookTimeout: 60000,  // 1 minute for setup/teardown hooks (<PERSON><PERSON> needs more time)
    // Enable parallel execution with worker isolation
    fileParallelism: true,
    maxConcurrency: 2, // Limit to 2 workers since each has its own Electron instance
    // Run tests in threads with worker isolation
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false, // Enable multiple threads for parallel execution
        isolate: true        // Isolate workers for better test isolation
      }
    }
  },
  define: {
    global: 'globalThis'
  }
});
