{"main": {"id": "main-workspace", "type": "split", "children": [{"id": "81dcc327f1d9f183", "type": "tabs", "children": [{"id": "9b4ab43223be6a97", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}]}], "direction": "vertical"}, "left": {"id": "left-sidebar", "type": "split", "children": [{"id": "898a709193a7161c", "type": "tabs", "children": [{"id": "6fe35d275e225bc4", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "97b9a171979df1f3", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "88fb6bb18715e950", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "right-sidebar", "type": "split", "children": [{"id": "2767c37c0625d839", "type": "tabs", "children": [{"id": "6713f86f51374e01", "type": "leaf", "state": {"type": "backlink", "state": {"file": "Welcome.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Welcome"}}, {"id": "9b723a37d8f875b7", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Welcome.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Welcome"}}, {"id": "cde484389af7c67b", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "28c0f489380b7baf", "type": "leaf", "state": {"type": "outline", "state": {"file": "Welcome.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Welcome"}}, {"id": "bb2da46be4de6633", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "8253c04e9bcdc146", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "5b660e23ff27cc6c", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "8b7615c443b6cf7f", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "5fbc20ba6b6c87d9", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "8177a6d313cf384e", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "b8530651673f8436", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "f07870e697490997", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "bda458f66e23a85d", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "f322f672b77ed25c", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}, {"id": "171ab46a43a5a08d", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}], "currentTab": 14}], "direction": "horizontal", "width": 300}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "9b4ab43223be6a97", "lastOpenFiles": ["articles/post-without-slug.md", "articles/no-slug-file.md", "articles/test-persistence.md", "articles/error-test-network-error.md", "articles/rails-and-its-ruby-dialect.md", "articles/introducing-elixir-drops.md", "articles/elixir-drops-020-with-support-for-custom-types-was-released.md", "articles/2024-status-update.md", "articles/speed-up-your-elixir-testing-with-custom-tasks-and-key-bindings-in-visual-studio-code.md", "articles/retiring-from-the-core-teams.md", "articles/announcing-textparser-for-elixir.md", "articles/til-capturing-logs-in-elixir-tests.md", "articles/introducing-drops-relation.md", "articles/test-post.md", "articles/debug-test.md", "articles/e2e-roundtrip-test.md", "articles/direct-command-sync-test.md", "articles/error-test-malformed-frontmatter.md", "articles/test-multiple-changes.md", "articles/error-test-no-frontmatter.md", "articles/test-changed-at-post.md", "articles/direct-command-test-post.md", "articles/error-test-invalid-url.md", "articles/ribbon-sync-test-post.md", "articles/ribbon-test-sync-current.md", "articles/e2e-sync-from-ghost-test.md", "articles/Untitled 1.base", "articles/Untitled.base"]}