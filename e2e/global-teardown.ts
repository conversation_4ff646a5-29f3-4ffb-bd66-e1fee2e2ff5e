import * as fs from 'fs';
import * as path from 'path';
import { cleanupSharedElectron } from './setup-shared-electron';
import { cleanupAllWorkerContexts } from './helpers/shared-context';

/**
 * Global teardown for all e2e tests
 * Cleans up the shared Electron instance and all worker contexts
 */
export default async function globalTeardown() {
  console.log("🌍 Starting global e2e test teardown...");

  try {
    // Clean up worker-specific contexts and isolated environments first
    await cleanupAllWorkerContexts();

    // Clean up shared Electron instance
    await cleanupSharedElectron();

    // Clean up connection file
    const connectionFile = path.join(process.cwd(), 'e2e/.electron-connection.json');
    if (fs.existsSync(connectionFile)) {
      fs.unlinkSync(connectionFile);
      console.log("🗑️ Connection file cleaned up");
    }

    console.log("✅ Global e2e test teardown complete");

  } catch (error) {
    console.error("❌ Global teardown failed:", error);
    // Don't throw here - we want tests to complete even if cleanup fails
  }
}
